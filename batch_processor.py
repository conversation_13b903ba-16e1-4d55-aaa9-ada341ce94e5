"""
Complete Multi-Athlete Batch Processor for Blast+HitTrax Data
============================================================

This file contains EVERYTHING needed - all functions included.
Just copy this entire file and run it!

Handles multiple input scenarios:
1. Folder with multiple HitTrax files: data (1).csv, data (2).csv, etc.
2. Folder with multiple Blast files
3. Zip files with multiple athletes
4. Mixed input sources

Automatically detects athletes and processes all combinations.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import glob
import zipfile
import tempfile
import shutil
from pathlib import Path
import re

def extract_athlete_from_filename(filename):
    """Extract athlete name from various filename patterns."""
    # Remove .csv extension first
    name_without_extension = filename.replace('.csv', '').replace('.CSV', '')
    
    # Pattern: "Metrics - <PERSON> Rodriguez - 2025-06-27..."
    if " - " in filename:
        parts = filename.split(" - ")
        if len(parts) >= 2:
            return parts[1].strip()
    
    # Pattern: "Metrics  Gavin Rodriguez  20250627..."
    if "Metrics" in filename:
        parts = filename.replace("Metrics", "").strip()
        # Extract name before the first date pattern
        import re
        name_match = re.match(r'^[^0-9]*', parts.strip())
        if name_match:
            return name_match.group().strip().strip('-').strip()
    
    # NEW: Simple pattern like "Abby Farrell.csv"
    if not "Metrics" in filename and not " - " in filename:
        return name_without_extension.strip()
    
    return "Unknown Athlete"

def parse_blast_data_complete(filepath, athlete_name=None):
    """Parse Blast Motion CSV file with ALL columns included."""
    try:
        # Read the file to find where data starts
        with open(filepath, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Find the line that starts with "Date,Equipment"
        data_start_idx = None
        for i, line in enumerate(lines):
            if line.startswith('Date,Equipment'):
                data_start_idx = i
                break
        
        if data_start_idx is None:
            raise ValueError("Could not find data start in Blast file")
        
        # Read data from the correct starting point
        df = pd.read_csv(filepath, skiprows=data_start_idx)
        
        # Parse timestamps
        df['timestamp'] = pd.to_datetime(df['Date'])
        
        # Extract athlete name from filename if not provided
        if athlete_name is None:
            athlete_name = extract_athlete_from_filename(os.path.basename(filepath))
        
        df['athlete'] = athlete_name
        
        # Add source info
        df['blast_source_file'] = os.path.basename(filepath)
        
        return df
        
    except Exception as e:
        print(f"❌ Error parsing Blast file {filepath}: {str(e)}")
        return None

def parse_hittrax_data_complete(filepath):
    """Parse HitTrax CSV file with ALL columns included."""
    try:
        df = pd.read_csv(filepath)
        
        # Clean column names (remove leading spaces but preserve for mapping)
        column_mapping = {col: col.strip() for col in df.columns}
        
        # Parse timestamps - handle the Date column (which may have leading space)
        date_col = None
        for col in df.columns:
            if 'Date' in col:
                date_col = col
                break
        
        if date_col:
            df['timestamp'] = pd.to_datetime(df[date_col])
        
        # Extract athlete name - handle User column (which may have leading space)
        user_col = None
        for col in df.columns:
            if 'User' in col:
                user_col = col
                break
        
        if user_col:
            df['athlete'] = df[user_col].str.strip()
        else:
            df['athlete'] = "Unknown Athlete"
        
        # Identify records with ball contact - handle Velo column
        velo_col = None
        for col in df.columns:
            if 'Velo' in col and 'Exit' not in col:  # Main velocity column
                velo_col = col
                break
        
        if velo_col:
            df['has_contact'] = (df[velo_col].notna()) & (df[velo_col] > 0)
        else:
            df['has_contact'] = False
        
        # Add source info
        df['hittrax_source_file'] = os.path.basename(filepath)
        
        return df, column_mapping
        
    except Exception as e:
        print(f"❌ Error parsing HitTrax file {filepath}: {str(e)}")
        return None, {}

def find_best_matches_complete(blast_df, hittrax_df, max_time_diff=5):
    """Enhanced matching with detailed logging."""
    matches = []
    used_blast_indices = set()
    used_hittrax_indices = set()
    
    # Only match HitTrax records with ball contact
    hittrax_contact = hittrax_df[hittrax_df['has_contact']].copy()
    
    print(f"  🔍 Matching {len(blast_df)} Blast records with {len(hittrax_contact)} HitTrax contact records")
    
    for _, hittrax_record in hittrax_contact.iterrows():
        best_match = None
        best_time_diff = float('inf')
        best_blast_idx = None
        
        # Find closest Blast record for same athlete
        blast_athlete_records = blast_df[
            (blast_df['athlete'] == hittrax_record['athlete']) &
            (~blast_df.index.isin(used_blast_indices))
        ]
        
        for blast_idx, blast_record in blast_athlete_records.iterrows():
            time_diff = abs((blast_record['timestamp'] - hittrax_record['timestamp']).total_seconds())
            
            if time_diff <= max_time_diff and time_diff < best_time_diff:
                best_match = blast_record
                best_time_diff = time_diff
                best_blast_idx = blast_idx
        
        if best_match is not None:
            # Determine confidence level
            if best_time_diff <= 3:
                confidence = 'High'
            elif best_time_diff <= 5:
                confidence = 'OK'
            else:
                confidence = 'Unmatched'
            
            matches.append({
                'blast_idx': best_blast_idx,
                'hittrax_idx': hittrax_record.name,
                'time_difference': best_time_diff,
                'confidence': confidence,
                'blast_record': best_match,
                'hittrax_record': hittrax_record
            })
            
            used_blast_indices.add(best_blast_idx)
            used_hittrax_indices.add(hittrax_record.name)
    
    return matches, used_blast_indices, used_hittrax_indices

def create_complete_merged_dataset(blast_df, hittrax_df, hittrax_columns, matches, used_blast_indices, used_hittrax_indices):
    """Create merged dataset with ALL columns from both sources."""
    merged_records = []
    
    # Add matched records with ALL columns
    for match in matches:
        blast_rec = match['blast_record']
        hittrax_rec = match['hittrax_record']
        
        merged_record = {}
        
        # Core info
        merged_record['Athlete'] = blast_rec['athlete']
        merged_record['Match_Type'] = 'Matched'
        merged_record['Confidence'] = match['confidence']
        merged_record['Time_Difference_Seconds'] = round(match['time_difference'], 3)
        
        # ALL Blast columns (prefix with "Blast_")
        for col in blast_df.columns:
            if col not in ['athlete', 'timestamp', 'blast_source_file']:
                blast_col_name = f"Blast_{col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')}"
                merged_record[blast_col_name] = blast_rec[col]
        
        merged_record['Blast_Source_File'] = blast_rec.get('blast_source_file', '')
        
        # ALL HitTrax columns (prefix with "HitTrax_")
        for col in hittrax_df.columns:
            if col not in ['athlete', 'timestamp', 'has_contact', 'hittrax_source_file']:
                hittrax_col_name = f"HitTrax_{col.strip().replace(' ', '_').replace('(', '').replace(')', '').replace('#', 'Number').replace('.', '_')}"
                merged_record[hittrax_col_name] = hittrax_rec[col]
        
        merged_record['HitTrax_Source_File'] = hittrax_rec.get('hittrax_source_file', '')
        
        merged_records.append(merged_record)
    
    # Add unmatched Blast records
    unmatched_blast = blast_df[~blast_df.index.isin(used_blast_indices)]
    for _, blast_rec in unmatched_blast.iterrows():
        merged_record = {}
        
        # Core info
        merged_record['Athlete'] = blast_rec['athlete']
        merged_record['Match_Type'] = 'Blast_Only'
        merged_record['Confidence'] = 'Unmatched'
        merged_record['Time_Difference_Seconds'] = ''
        
        # ALL Blast columns
        for col in blast_df.columns:
            if col not in ['athlete', 'timestamp', 'blast_source_file']:
                blast_col_name = f"Blast_{col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')}"
                merged_record[blast_col_name] = blast_rec[col]
        
        merged_record['Blast_Source_File'] = blast_rec.get('blast_source_file', '')
        
        # Empty HitTrax columns
        for col in hittrax_df.columns:
            if col not in ['athlete', 'timestamp', 'has_contact', 'hittrax_source_file']:
                hittrax_col_name = f"HitTrax_{col.strip().replace(' ', '_').replace('(', '').replace(')', '').replace('#', 'Number').replace('.', '_')}"
                merged_record[hittrax_col_name] = ''
        
        merged_record['HitTrax_Source_File'] = ''
        
        merged_records.append(merged_record)
    
    # Add unmatched HitTrax records
    unmatched_hittrax = hittrax_df[~hittrax_df.index.isin(used_hittrax_indices)]
    for _, hittrax_rec in unmatched_hittrax.iterrows():
        match_type = 'HitTrax_Only_Contact' if hittrax_rec['has_contact'] else 'HitTrax_Only_No_Contact'
        
        merged_record = {}
        
        # Core info
        merged_record['Athlete'] = hittrax_rec['athlete']
        merged_record['Match_Type'] = match_type
        merged_record['Confidence'] = 'Unmatched'
        merged_record['Time_Difference_Seconds'] = ''
        
        # Empty Blast columns
        blast_columns_sample = ['Date', 'Equipment', 'Handedness', 'Swing Details', 'Plane Score', 
                               'Connection Score', 'Rotation Score', 'Bat Speed (mph)', 
                               'Rotational Acceleration (g)', 'On Plane Efficiency (%)', 
                               'Attack Angle (deg)', 'Early Connection (deg)', 'Connection at Impact (deg)', 
                               'Vertical Bat Angle (deg)', 'Power (kW)', 'Time to Contact (sec)', 
                               'Peak Hand Speed (mph)', 'Exit Velocity (mph)', 'Launch Angle (deg)', 
                               'Estimated Distance (feet)']
        
        for col in blast_columns_sample:
            blast_col_name = f"Blast_{col.replace(' ', '_').replace('(', '').replace(')', '').replace('%', 'Pct')}"
            merged_record[blast_col_name] = ''
        
        merged_record['Blast_Source_File'] = ''
        
        # ALL HitTrax columns
        for col in hittrax_df.columns:
            if col not in ['athlete', 'timestamp', 'has_contact', 'hittrax_source_file']:
                hittrax_col_name = f"HitTrax_{col.strip().replace(' ', '_').replace('(', '').replace(')', '').replace('#', 'Number').replace('.', '_')}"
                merged_record[hittrax_col_name] = hittrax_rec[col]
        
        merged_record['HitTrax_Source_File'] = hittrax_rec.get('hittrax_source_file', '')
        
        merged_records.append(merged_record)
    
    return pd.DataFrame(merged_records)

def deduplicate_merged_data(df, context="data"):
    """
    Deduplicate merged data using intelligent column selection.

    Args:
        df: DataFrame to deduplicate
        context: String describing the context for logging

    Returns:
        Deduplicated DataFrame
    """
    if df.empty:
        return df

    original_count = len(df)

    # Core columns that should always be used for deduplication
    dedup_columns = ['Athlete', 'Match_Type', 'Confidence']

    # Add Time_Difference_Seconds if it exists and has meaningful values
    if 'Time_Difference_Seconds' in df.columns:
        # Only include if it has non-empty values for matched records
        if df[df['Match_Type'] == 'Matched']['Time_Difference_Seconds'].notna().any():
            dedup_columns.append('Time_Difference_Seconds')

    # Add timestamp columns (both Blast and HitTrax)
    timestamp_cols = [col for col in df.columns if any(indicator in col for indicator in ['Blast_Date', 'HitTrax_Date', '_Date'])]
    dedup_columns.extend(timestamp_cols)

    # Add key performance metrics for more precise deduplication
    performance_metrics = []
    for col in df.columns:
        if any(metric in col for metric in ['Bat_Speed', 'Exit_Velocity', 'Launch_Angle', 'Velo', 'Distance']):
            performance_metrics.append(col)

    # Limit performance metrics to avoid over-specification
    dedup_columns.extend(performance_metrics[:5])

    # Only use columns that actually exist in the DataFrame
    dedup_columns = [col for col in dedup_columns if col in df.columns]

    # Remove duplicates
    if dedup_columns:
        deduplicated_df = df.drop_duplicates(subset=dedup_columns, keep='first')
        removed_count = original_count - len(deduplicated_df)

        if removed_count > 0:
            print(f"   🧹 {context}: Removed {removed_count} duplicates ({original_count} → {len(deduplicated_df)} records)")
        else:
            print(f"   ✅ {context}: No duplicates found ({len(deduplicated_df)} records)")

        return deduplicated_df
    else:
        print(f"   ⚠️ {context}: No suitable columns for deduplication")
        return df

def merge_single_athlete(blast_file, hittrax_file, athlete_name=None):
    """Merge data for a single athlete with complete columns."""
    print(f"\n🔄 Processing athlete data...")

    # Handle cases where one file type might be missing
    if blast_file is None and hittrax_file is None:
        print("   ❌ Both files are None")
        return None

    blast_df = None
    hittrax_df = None
    hittrax_columns = {}

    # Load Blast data if available
    if blast_file is not None:
        print(f"   📊 Blast: {os.path.basename(blast_file)}")
        blast_df = parse_blast_data_complete(blast_file, athlete_name)
        if blast_df is None:
            print("   ❌ Failed to parse Blast file")
            if hittrax_file is None:  # If both fail, return None
                return None
    else:
        print("   📊 Blast: None (creating empty dataset)")

    # Load HitTrax data if available
    if hittrax_file is not None:
        print(f"   ⚾ HitTrax: {os.path.basename(hittrax_file)}")
        hittrax_df, hittrax_columns = parse_hittrax_data_complete(hittrax_file)
        if hittrax_df is None:
            print("   ❌ Failed to parse HitTrax file")
            if blast_file is None:  # If both fail, return None
                return None
    else:
        print("   ⚾ HitTrax: None (creating empty dataset)")

    # Create empty dataframes if one source is missing
    if blast_df is None and hittrax_df is not None:
        # Create minimal empty blast dataframe
        blast_df = pd.DataFrame({
            'athlete': [athlete_name or 'Unknown'],
            'timestamp': [pd.NaT],
            'blast_source_file': ['']
        })

    if hittrax_df is None and blast_df is not None:
        # Create minimal empty hittrax dataframe
        hittrax_df = pd.DataFrame({
            'athlete': [athlete_name or 'Unknown'],
            'timestamp': [pd.NaT],
            'has_contact': [False],
            'hittrax_source_file': ['']
        })
    
    print(f"   📈 Loaded {len(blast_df)} Blast records, {len(hittrax_df)} HitTrax records")
    print(f"   🎯 {hittrax_df['has_contact'].sum()} HitTrax records with ball contact")
    
    # Find matches
    matches, used_blast_indices, used_hittrax_indices = find_best_matches_complete(blast_df, hittrax_df)
    
    # Create complete merged dataset
    merged_df = create_complete_merged_dataset(blast_df, hittrax_df, hittrax_columns, matches, used_blast_indices, used_hittrax_indices)
    
    high_confidence = sum(1 for m in matches if m['confidence'] == 'High')
    ok_confidence = sum(1 for m in matches if m['confidence'] == 'OK')
    
    print(f"   ✅ Found {len(matches)} matches (High: {high_confidence}, OK: {ok_confidence})")
    print(f"   📝 Complete dataset: {len(merged_df)} records with {len(merged_df.columns)} columns")
    
    return merged_df

def find_hittrax_files(folder_path):
    """Find all HitTrax files in a folder (data (1).csv, data (2).csv, etc.)"""
    hittrax_files = []
    
    # Pattern 1: data (1).csv, data (2).csv, etc.
    pattern1 = os.path.join(folder_path, "data (*).csv")
    files1 = glob.glob(pattern1)
    
    # Pattern 2: Any CSV that looks like HitTrax data
    pattern2 = os.path.join(folder_path, "*.csv")
    all_csvs = glob.glob(pattern2)
    
    for file in all_csvs:
        # Quick check if it's HitTrax format (has columns like Date, User, Velo)
        try:
            sample_df = pd.read_csv(file, nrows=1)
            if any('Date' in col for col in sample_df.columns) and any('User' in col for col in sample_df.columns):
                hittrax_files.append(file)
        except:
            continue
    
    # Remove duplicates and sort
    hittrax_files = sorted(list(set(hittrax_files)))
    return hittrax_files

def find_blast_files(folder_path):
    """Find all Blast Motion files in a folder"""
    blast_files = []
    
    # Pattern 1: Metrics files
    pattern1 = os.path.join(folder_path, "Metrics*.csv")
    files1 = glob.glob(pattern1)
    
    # Pattern 2: Any CSV that looks like Blast data
    pattern2 = os.path.join(folder_path, "*.csv")
    all_csvs = glob.glob(pattern2)
    
    for file in all_csvs:
        # Quick check if it's Blast format
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # First 1000 chars
                if 'Blast Motion' in content or 'Date,Equipment' in content:
                    blast_files.append(file)
        except:
            continue
    
    # Remove duplicates and sort
    blast_files = sorted(list(set(blast_files)))
    return blast_files

def extract_athlete_from_hittrax_file(filepath):
    """Extract athlete name from HitTrax file content"""
    try:
        df = pd.read_csv(filepath, nrows=10)  # Just read first few rows
        
        # Find User column (may have leading space)
        user_col = None
        for col in df.columns:
            if 'User' in col:
                user_col = col
                break
        
        if user_col:
            # Get the most common non-null athlete name
            athletes = df[user_col].dropna().str.strip()
            if not athletes.empty:
                return athletes.mode()[0]  # Most frequent value
        
        return None
    except:
        return None

def extract_athletes_from_files(hittrax_files, blast_files):
    """Extract all unique athlete names from the files"""
    athletes = set()
    
    # From HitTrax files
    for file in hittrax_files:
        athlete = extract_athlete_from_hittrax_file(file)
        if athlete and athlete != "Unknown Athlete":
            athletes.add(athlete)
    
    # From Blast files
    for file in blast_files:
        athlete = extract_athlete_from_filename(os.path.basename(file))
        if athlete and athlete != "Unknown Athlete":
            athletes.add(athlete)
    
    return sorted(list(athletes))

def match_files_by_athlete(hittrax_files, blast_files, athletes):
    """Create athlete-to-files mapping"""
    athlete_files = {}
    
    for athlete in athletes:
        athlete_files[athlete] = {
            'hittrax_files': [],
            'blast_files': []
        }
        
        # Find HitTrax files for this athlete
        for file in hittrax_files:
            file_athlete = extract_athlete_from_hittrax_file(file)
            if file_athlete and file_athlete.lower() == athlete.lower():
                athlete_files[athlete]['hittrax_files'].append(file)
        
        # Find Blast files for this athlete
        for file in blast_files:
            file_athlete = extract_athlete_from_filename(os.path.basename(file))
            if file_athlete and file_athlete.lower() == athlete.lower():
                athlete_files[athlete]['blast_files'].append(file)
    
    return athlete_files

def batch_process_athletes(input_folder, output_folder=None):
    """
    Main batch processing function
    
    Args:
        input_folder: Folder containing CSV files
        output_folder: Where to save results (optional)
    """
    
    if output_folder is None:
        output_folder = os.path.join(input_folder, "merged_results")
    
    # Create output folder
    os.makedirs(output_folder, exist_ok=True)
    
    print("🚀 MULTI-ATHLETE BATCH PROCESSOR")
    print("=" * 50)
    print(f"📁 Input folder: {input_folder}")
    print(f"📁 Output folder: {output_folder}")
    
    # Process regular folder
    hittrax_files = find_hittrax_files(input_folder)
    blast_files = find_blast_files(input_folder)
    
    print(f"📊 Found {len(hittrax_files)} HitTrax files")
    print(f"🎯 Found {len(blast_files)} Blast files")
    
    if len(hittrax_files) == 0 and len(blast_files) == 0:
        print("❌ No CSV files found in the input folder!")
        return []
    
    # Extract athlete names
    athletes = extract_athletes_from_files(hittrax_files, blast_files)
    print(f"👥 Detected {len(athletes)} athletes: {', '.join(athletes)}")
    
    # Match files to athletes
    athlete_files = match_files_by_athlete(hittrax_files, blast_files, athletes)
    
    # Process each athlete
    all_merged_data = []
    processing_summary = []
    
    for athlete in athletes:
        print(f"\n🏃 Processing {athlete}...")
        
        athlete_data = athlete_files[athlete]
        hittrax_count = len(athlete_data['hittrax_files'])
        blast_count = len(athlete_data['blast_files'])
        
        print(f"   📊 HitTrax files: {hittrax_count}")
        print(f"   🎯 Blast files: {blast_count}")
        
        if hittrax_count == 0 and blast_count == 0:
            print(f"   ⚠️ No files found for {athlete}")
            continue
        
        # Merge all combinations for this athlete
        athlete_merged_data = []
        
        # If we have both types, merge strategically to minimize duplicates
        if hittrax_count > 0 and blast_count > 0:
            print(f"   🔄 Processing {hittrax_count} HitTrax × {blast_count} Blast file combinations...")

            for hittrax_file in athlete_data['hittrax_files']:
                for blast_file in athlete_data['blast_files']:
                    merged_data = merge_single_athlete(blast_file, hittrax_file, athlete)
                    if merged_data is not None:
                        athlete_merged_data.append(merged_data)

        # Handle cases where we only have one type of data
        elif hittrax_count > 0:
            print(f"   📊 Processing {hittrax_count} HitTrax files (no Blast data)")
            # Process HitTrax-only data
            for hittrax_file in athlete_data['hittrax_files']:
                # Create empty blast data for consistency
                empty_blast_file = None  # This will be handled in merge_single_athlete
                merged_data = merge_single_athlete(empty_blast_file, hittrax_file, athlete)
                if merged_data is not None:
                    athlete_merged_data.append(merged_data)

        elif blast_count > 0:
            print(f"   🎯 Processing {blast_count} Blast files (no HitTrax data)")
            # Process Blast-only data
            for blast_file in athlete_data['blast_files']:
                # Create empty hittrax data for consistency
                empty_hittrax_file = None  # This will be handled in merge_single_athlete
                merged_data = merge_single_athlete(blast_file, empty_hittrax_file, athlete)
                if merged_data is not None:
                    athlete_merged_data.append(merged_data)
        
        # Combine all data for this athlete
        if athlete_merged_data:
            combined_athlete_data = pd.concat(athlete_merged_data, ignore_index=True)

            # DEDUPLICATION: Use the dedicated function
            combined_athlete_data = deduplicate_merged_data(combined_athlete_data, f"{athlete} athlete data")

            all_merged_data.append(combined_athlete_data)

            # Save individual athlete file
            athlete_output_file = os.path.join(output_folder, f"{athlete.replace(' ', '_')}_complete_data.csv")
            combined_athlete_data.to_csv(athlete_output_file, index=False)
            
            processing_summary.append({
                'athlete': athlete,
                'records': len(combined_athlete_data),
                'matches': len(combined_athlete_data[combined_athlete_data['Match_Type'] == 'Matched']),
                'blast_files': blast_count,
                'hittrax_files': hittrax_count,
                'output_file': athlete_output_file
            })
            
            print(f"   ✅ Saved {len(combined_athlete_data)} records to {os.path.basename(athlete_output_file)}")
    
    # Create master combined file
    if all_merged_data:
        master_data = pd.concat(all_merged_data, ignore_index=True)

        # FINAL DEDUPLICATION: Remove any remaining duplicates across all athletes
        print(f"\n� Creating master file...")
        master_data = deduplicate_merged_data(master_data, "Master file")

        master_output_file = os.path.join(output_folder, "ALL_ATHLETES_complete_data.csv")
        master_data.to_csv(master_output_file, index=False)
        
        print(f"\n📋 PROCESSING SUMMARY")
        print("=" * 30)
        total_records = 0
        total_matches = 0
        
        for summary in processing_summary:
            print(f"👤 {summary['athlete']}: {summary['records']} records, {summary['matches']} matches")
            total_records += summary['records']
            total_matches += summary['matches']
        
        print(f"\n🎉 BATCH PROCESSING COMPLETE!")
        print(f"📊 Total records: {total_records}")
        print(f"✅ Total matches: {total_matches}")
        print(f"👥 Athletes processed: {len(processing_summary)}")
        print(f"💾 Master file: {master_output_file}")
        print(f"📁 Individual files saved to: {output_folder}")
        print(f"\n🧹 DEDUPLICATION APPLIED:")
        print(f"   • Per-athlete deduplication during processing")
        print(f"   • Final master file deduplication")
        print(f"   • Intelligent column selection for duplicate detection")
    
    return processing_summary

# Example usage scenarios
if __name__ == "__main__":
    input_folder = "C:/Users/<USER>/OneDrive/Desktop/BlastHittrax_MultiAthlete"
    batch_process_athletes(input_folder)